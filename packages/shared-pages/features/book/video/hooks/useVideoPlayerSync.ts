import { type MediaResourceServicePlayDTO } from '@jgl/biz-func';
import { showToast } from '@jgl/utils';
import { useCallback } from 'react';
import { useSharedDraggableMenuContext } from '../../context/SharedDraggableMenuContext';
import { useFastSync } from './useFastSync';
import { usePreSync } from './usePreSync';

/**
 * 管理两个VideoPlayer之间的状态同步
 * - inlineVideoPlayer: 内联播放器
 * - fullscreenVideoPlayer: 全屏播放器
 *
 * 优化版本：使用智能同步策略，提供大厂级用户体验（Modal版本）
 */
export const useVideoPlayerSync = () => {
  const {
    inlineVideoPlayer,
    fullscreenVideoPlayer,
    selectedVideoResource,
    isVideoFullscreen,
    isVideoFinished,
    videoCurrentTime,
    setIsVideoFullscreen,
  } = useSharedDraggableMenuContext();

  const { prepareTargetPlayer } = usePreSync();
  const { smartSync, instantSync } = useFastSync();

  // const { pauseAudio } = useFloatingAudioBallContext() ?? {};

  const videoDuration = selectedVideoResource?.ext?.times || 0;

  // 预同步：在用户可能进入全屏前就开始准备
  const handleFullscreenPrepare = useCallback(() => {
    if (inlineVideoPlayer && fullscreenVideoPlayer) {
      prepareTargetPlayer(inlineVideoPlayer, fullscreenVideoPlayer);
    }
  }, [inlineVideoPlayer, fullscreenVideoPlayer, prepareTargetPlayer]);

  // 降级策略：进入全屏失败时的备用方案
  const fallbackEnterFullscreen = useCallback(
    async (operationId: number) => {
      console.log(`[${operationId}] 执行降级策略：进入全屏`);

      try {
        // 简单的状态同步作为降级方案
        if (inlineVideoPlayer && fullscreenVideoPlayer) {
          fullscreenVideoPlayer.currentTime = inlineVideoPlayer.currentTime;
          fullscreenVideoPlayer.playbackRate = inlineVideoPlayer.playbackRate;

          // 延迟播放（降级方案，比原来的300ms快）
          setTimeout(() => {
            fullscreenVideoPlayer.play();
          }, 100);
        }
      } catch (error) {
        console.error(`[${operationId}] 降级策略也失败了:`, error);
        showToast({ title: '视频切换失败，请重试' });
      }
    },
    [inlineVideoPlayer, fullscreenVideoPlayer],
  );

  // 降级策略：退出全屏失败时的备用方案
  const fallbackExitFullscreen = useCallback(
    async (operationId: number) => {
      console.log(`[${operationId}] 执行降级策略：退出全屏`);

      try {
        // 简单的状态同步
        if (inlineVideoPlayer && fullscreenVideoPlayer) {
          inlineVideoPlayer.currentTime = fullscreenVideoPlayer.currentTime;
          inlineVideoPlayer.playbackRate = fullscreenVideoPlayer.playbackRate;

          // 延迟播放（降级方案）
          setTimeout(() => {
            inlineVideoPlayer.play();
          }, 100);
        }
      } catch (error) {
        console.error(`[${operationId}] 退出全屏降级策略失败:`, error);
        // 最终降级：直接播放内联播放器
        try {
          inlineVideoPlayer?.play();
        } catch (finalError) {
          console.error(`[${operationId}] 最终降级也失败:`, finalError);
        }
      }
    },
    [inlineVideoPlayer, fullscreenVideoPlayer],
  );

  // 错误恢复：确保界面状态正确
  const handleSyncError = useCallback(
    async (error: Error, operationId: number) => {
      console.error(`[${operationId}] 同步错误恢复:`, error);

      // 恢复界面状态
      setIsVideoFullscreen?.(false);

      // 确保内联播放器可以继续播放
      try {
        inlineVideoPlayer?.play();
      } catch (playError) {
        console.error(`[${operationId}] 恢复内联播放失败:`, playError);
      }

      showToast({ title: '视频切换失败，已恢复正常播放' });
    },
    [setIsVideoFullscreen, inlineVideoPlayer],
  );

  // 播放视频（两个播放器都加载相同内容）
  const playVideo = useCallback(
    async (resInfo: MediaResourceServicePlayDTO, autoPlay = true) => {
      console.log('royrao: 准备播放 - ', { autoPlay, resInfo });

      if (!resInfo?.url || !inlineVideoPlayer || !fullscreenVideoPlayer) {
        return;
      }

      // 两个播放器都加载相同的视频
      await Promise.all([
        inlineVideoPlayer.replaceAsync(resInfo.url),
        fullscreenVideoPlayer.replaceAsync(resInfo.url),
      ]);

      // 检查播放器状态
      if (
        inlineVideoPlayer.status === 'error' ||
        fullscreenVideoPlayer.status === 'error'
      ) {
        showToast({ title: '视频播放失败' });
        return;
      }

      if (autoPlay) {
        if (isVideoFullscreen) {
          fullscreenVideoPlayer.replay();
          fullscreenVideoPlayer.play();
        } else {
          inlineVideoPlayer.replay();
          inlineVideoPlayer.play();
        }

        // pauseAudio?.();
      } else {
        inlineVideoPlayer.pause();
        fullscreenVideoPlayer.pause();
      }
    },
    [inlineVideoPlayer, fullscreenVideoPlayer, isVideoFullscreen],
  );

  // 暂停视频
  const pauseVideo = useCallback(() => {
    if (isVideoFullscreen) {
      fullscreenVideoPlayer?.pause();
    } else {
      inlineVideoPlayer?.pause();
    }
  }, [inlineVideoPlayer, fullscreenVideoPlayer, isVideoFullscreen]);

  // 播放/暂停切换
  const handlePressPlayOrPause = useCallback(() => {
    const currentPlayer = isVideoFullscreen
      ? fullscreenVideoPlayer
      : inlineVideoPlayer;

    if (!currentPlayer) {
      return;
    }

    if (currentPlayer.playing) {
      currentPlayer.pause();
    } else {
      if (isVideoFinished?.value) {
        currentPlayer.replay();
      }
      currentPlayer.play();
      /* 暂停音频会暂停视频，暂时移除 */
      // pauseAudio?.();
    }
  }, [
    isVideoFullscreen,
    fullscreenVideoPlayer,
    inlineVideoPlayer,
    isVideoFinished?.value,
  ]);

  // 进入全屏 - 优化版本：智能同步 + 大厂级体验
  const handleEnterFullscreen = useCallback(async () => {
    if (!inlineVideoPlayer || !fullscreenVideoPlayer) {
      console.warn('播放器实例无效，无法进入全屏');
      return;
    }

    const operationId = Date.now();
    console.log(`[${operationId}] 开始进入全屏`);

    try {
      // 1. 立即暂停内联播放器
      inlineVideoPlayer.pause();

      // 2. 使用即时同步快速准备状态
      instantSync(inlineVideoPlayer, fullscreenVideoPlayer);

      // 3. 立即显示Modal（不等待完整同步）
      setIsVideoFullscreen?.(true);
      console.log(`[${operationId}] Modal已显示`);

      // 4. 在后台执行完整的智能同步
      const syncResult = await smartSync(
        inlineVideoPlayer,
        fullscreenVideoPlayer,
        {
          maxRetries: 2,
          retryDelay: 30,
        },
      );

      if (syncResult.success) {
        // 同步成功：立即播放，无延迟
        fullscreenVideoPlayer.play();
        console.log(
          `[${operationId}] 全屏切换成功，同步耗时: ${syncResult.duration.toFixed(
            2,
          )}ms`,
        );
      } else {
        // 同步失败：使用降级策略
        console.warn(`[${operationId}] 使用降级策略`);
        await fallbackEnterFullscreen(operationId);
      }
    } catch (error) {
      console.error(`[${operationId}] 全屏切换失败:`, error);
      await handleSyncError(error as Error, operationId);
    }
  }, [
    fallbackEnterFullscreen,
    fullscreenVideoPlayer,
    handleSyncError,
    inlineVideoPlayer,
    instantSync,
    setIsVideoFullscreen,
    smartSync,
  ]);

  // 退出全屏 - 优化版本：快速响应 + 智能同步
  const handleExitFullscreen = useCallback(async () => {
    if (!inlineVideoPlayer || !fullscreenVideoPlayer) {
      console.warn('播放器实例无效，无法退出全屏');
      return;
    }

    const operationId = Date.now();
    console.log(`[${operationId}] 开始退出全屏`);

    try {
      // 1. 立即暂停全屏播放器
      fullscreenVideoPlayer.pause();

      // 2. 立即关闭Modal，不等待状态同步完成
      setIsVideoFullscreen?.(false);
      console.log(`[${operationId}] Modal已关闭`);

      // 3. 使用即时同步快速准备状态
      instantSync(fullscreenVideoPlayer, inlineVideoPlayer);

      // 4. 在后台执行完整的智能同步
      const syncResult = await smartSync(
        fullscreenVideoPlayer,
        inlineVideoPlayer,
        {
          maxRetries: 1, // 退出全屏时减少重试次数，优先速度
          retryDelay: 20,
        },
      );

      if (syncResult.success) {
        // 同步成功：立即播放内联播放器
        inlineVideoPlayer.play();
        console.log(
          `[${operationId}] 退出全屏成功，同步耗时: ${syncResult.duration.toFixed(
            2,
          )}ms`,
        );
      } else {
        // 同步失败：使用降级策略
        console.warn(`[${operationId}] 使用降级策略退出全屏`);
        await fallbackExitFullscreen(operationId);
      }
    } catch (error) {
      console.error(`[${operationId}] 退出全屏失败:`, error);
      // 退出全屏的错误恢复相对简单，确保UI状态正确即可
      setIsVideoFullscreen?.(false);
    }
  }, [
    fallbackExitFullscreen,
    fullscreenVideoPlayer,
    inlineVideoPlayer,
    instantSync,
    setIsVideoFullscreen,
    smartSync,
  ]);

  // 跳转到指定时间
  const handleSeek = useCallback(
    (value: number) => {
      const seekTime = Math.max(
        0,
        Math.min((value / 100) * videoDuration, videoDuration),
      );

      const currentPlayer = isVideoFullscreen
        ? fullscreenVideoPlayer
        : inlineVideoPlayer;
      if (currentPlayer) {
        currentPlayer.currentTime = seekTime;

        if (videoCurrentTime !== undefined) {
          videoCurrentTime.value = seekTime;
        }
      }
    },
    [
      fullscreenVideoPlayer,
      inlineVideoPlayer,
      videoCurrentTime,
      videoDuration,
      isVideoFullscreen,
    ],
  );

  // 改变播放速率
  const handleChangePlaybackRate = useCallback(
    (rate: number) => {
      if (rate < 0 || rate > 16) {
        return;
      }

      const currentPlayer = isVideoFullscreen
        ? fullscreenVideoPlayer
        : inlineVideoPlayer;
      if (currentPlayer && rate !== currentPlayer.playbackRate) {
        currentPlayer.playbackRate = rate;
      }
    },
    [inlineVideoPlayer, fullscreenVideoPlayer, isVideoFullscreen],
  );

  return {
    videoDuration,
    playVideo,
    pauseVideo,
    handlePressPlayOrPause,
    handleEnterFullscreen,
    handleExitFullscreen,
    handleSeek,
    handleChangePlaybackRate,
    handleFullscreenPrepare,
  };
};
