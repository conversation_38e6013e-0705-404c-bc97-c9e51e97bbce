import {
  addOrientationChangeListener,
  getOrientationAsync,
  Orientation,
  removeOrientationChangeListener,
} from 'expo-screen-orientation';
import { useCallback, useEffect } from 'react';
import { useSharedValue, withTiming } from 'react-native-reanimated';

/**
 * 全屏视频播放器方向管理hook
 * 根据设备方向自动调整播放器旋转角度
 */
export const useFullscreenOrientation = (isFullscreen: boolean) => {
  // 旋转角度，默认90度（保持现有逻辑）
  const rotationDegree = useSharedValue(90);

  // 处理方向变化
  const handleOrientationChange = useCallback(
    async (orientation: Orientation) => {
      let targetDegree = 90; // 默认角度

      switch (orientation) {
        case Orientation.LANDSCAPE_LEFT:
          targetDegree = -90;
          break;
        case Orientation.LANDSCAPE_RIGHT:
          targetDegree = 90;
          break;
        default:
          // 其他方向保持默认90度
          targetDegree = 90;
          break;
      }

      // 使用动画过渡到目标角度
      rotationDegree.value = withTiming(targetDegree, {
        duration: 300,
      });
    },
    [rotationDegree],
  );

  // 监听设备方向变化
  useEffect(() => {
    if (!isFullscreen) {
      // 非全屏状态下重置为默认角度
      rotationDegree.value = withTiming(90, { duration: 300 });
      return;
    }

    let subscription: ReturnType<typeof addOrientationChangeListener> | null =
      null;

    const setupOrientationListener = async () => {
      try {
        // 获取当前方向并设置初始角度
        const currentOrientation = await getOrientationAsync();
        await handleOrientationChange(currentOrientation);

        // 监听方向变化
        subscription = addOrientationChangeListener(({ orientationInfo }) => {
          handleOrientationChange(orientationInfo.orientation);
        });
      } catch (error) {
        console.warn('Failed to setup orientation listener:', error);
      }
    };

    setupOrientationListener();

    return () => {
      if (subscription) {
        removeOrientationChangeListener(subscription);
      }
    };
  }, [isFullscreen, handleOrientationChange, rotationDegree]);

  return {
    rotationDegree,
  };
};
