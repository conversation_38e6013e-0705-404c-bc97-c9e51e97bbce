import { Accelerometer } from 'expo-sensors';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useSharedValue, withTiming } from 'react-native-reanimated';

// 定义设备物理方向常量
const PHYSICAL_ORIENTATION = {
  LANDSCAPE_LEFT: 'LANDSCAPE_LEFT',
  LANDSCAPE_RIGHT: 'LANDSCAPE_RIGHT',
  OTHER: 'OTHER', // 包括竖屏和其他方向
} as const;

type PhysicalOrientation =
  (typeof PHYSICAL_ORIENTATION)[keyof typeof PHYSICAL_ORIENTATION];

/**
 * 全屏视频播放器方向管理hook
 * 使用加速度计检测设备物理方向，根据方向自动调整播放器旋转角度
 */
export const useFullscreenOrientation = (isFullscreen: boolean) => {
  // 旋转角度，默认90度（保持现有逻辑）
  const rotationDegree = useSharedValue(90);

  // 当前检测到的物理方向
  const [currentOrientation, setCurrentOrientation] =
    useState<PhysicalOrientation>(PHYSICAL_ORIENTATION.OTHER);

  // 防抖相关
  const debounceTimerRef = useRef<number | null>(null);
  const lastOrientationRef = useRef<PhysicalOrientation>(
    PHYSICAL_ORIENTATION.OTHER,
  );

  // 处理方向变化
  const handleOrientationChange = useCallback(
    (newOrientation: PhysicalOrientation) => {
      // 防抖：避免频繁切换
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
        if (lastOrientationRef.current !== newOrientation) {
          lastOrientationRef.current = newOrientation;
          setCurrentOrientation(newOrientation);

          let targetDegree = 90; // 默认角度

          switch (newOrientation) {
            case PHYSICAL_ORIENTATION.LANDSCAPE_LEFT:
              targetDegree = 90; // 修复：左倾时应该是90度
              break;
            case PHYSICAL_ORIENTATION.LANDSCAPE_RIGHT:
              targetDegree = -90; // 修复：右倾时应该是-90度
              break;
            default:
              // 其他方向保持默认90度
              targetDegree = 90;
              break;
          }

          // 使用动画过渡到目标角度
          rotationDegree.value = withTiming(targetDegree, {
            duration: 300,
          });

          console.log(
            `🔄 设备方向变化: ${newOrientation}, 旋转角度: ${targetDegree}度`,
          );
        }
      }, 150); // 150ms防抖延迟
    },
    [rotationDegree],
  );

  // 监听加速度计数据
  useEffect(() => {
    if (!isFullscreen) {
      // 非全屏状态下重置为默认角度和方向
      rotationDegree.value = withTiming(90, { duration: 300 });
      setCurrentOrientation(PHYSICAL_ORIENTATION.OTHER);
      lastOrientationRef.current = PHYSICAL_ORIENTATION.OTHER;

      // 清理防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      return;
    }

    let subscription: { remove: () => void } | null = null;

    const setupAccelerometerListener = async () => {
      try {
        // 设置更新频率：每200ms更新一次，平衡性能和响应性
        Accelerometer.setUpdateInterval(200);

        // 监听加速度计数据
        subscription = Accelerometer.addListener(({ x, y }) => {
          // 计算设备与地面的垂直角度
          // y轴表示垂直方向的重力分量，接近±1时表示设备接近垂直
          const verticalAngle = Math.abs(
            Math.atan2(Math.abs(x), Math.abs(y)) * (180 / Math.PI),
          );

          // 只有当设备与地面垂直角度在45度内时，才检测横屏方向
          // 这样可以避免手机平放时的误触发
          const isNearVertical = verticalAngle <= 45;

          let detectedOrientation: PhysicalOrientation =
            PHYSICAL_ORIENTATION.OTHER;

          if (isNearVertical) {
            // 在接近垂直状态下，根据x轴分量判断横屏方向
            // 阈值设为0.7，避免轻微倾斜时的误判
            if (x > 0.7) {
              // 设备向右倾斜 -> LANDSCAPE_RIGHT
              detectedOrientation = PHYSICAL_ORIENTATION.LANDSCAPE_RIGHT;
            } else if (x < -0.7) {
              // 设备向左倾斜 -> LANDSCAPE_LEFT
              detectedOrientation = PHYSICAL_ORIENTATION.LANDSCAPE_LEFT;
            }
          }
          // 其他情况（非垂直状态或倾斜角度不够）保持OTHER状态

          // 调试日志：显示当前检测状态
          if (__DEV__) {
            console.log(
              `📱 加速度计数据: x=${x.toFixed(2)}, y=${y.toFixed(
                2,
              )}, 垂直角度=${verticalAngle.toFixed(
                1,
              )}°, 接近垂直=${isNearVertical}, 检测方向=${detectedOrientation}`,
            );
          }

          handleOrientationChange(detectedOrientation);
        });

        console.log('加速度计监听已启动');
      } catch (error) {
        console.warn('启动加速度计监听失败:', error);
      }
    };

    setupAccelerometerListener();

    return () => {
      if (subscription) {
        subscription.remove();
        console.log('加速度计监听已停止');
      }

      // 清理防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
    };
  }, [isFullscreen, handleOrientationChange, rotationDegree]);

  return {
    rotationDegree,
    currentOrientation, // 可选：暴露当前方向用于调试
  };
};
