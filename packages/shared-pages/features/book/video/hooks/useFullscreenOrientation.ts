import { Gyroscope } from 'expo-sensors';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useSharedValue, withTiming } from 'react-native-reanimated';

// 定义设备物理方向常量
const PHYSICAL_ORIENTATION = {
  LANDSCAPE_LEFT: 'LANDSCAPE_LEFT',
  LANDSCAPE_RIGHT: 'LANDSCAPE_RIGHT',
  OTHER: 'OTHER', // 包括竖屏和其他方向
} as const;

type PhysicalOrientation =
  (typeof PHYSICAL_ORIENTATION)[keyof typeof PHYSICAL_ORIENTATION];

/**
 * 全屏视频播放器方向管理hook
 * 使用陀螺仪检测设备旋转状态，根据方向自动调整播放器旋转角度
 */
export const useFullscreenOrientation = (isFullscreen: boolean) => {
  // 旋转角度，默认90度（保持现有逻辑）
  const rotationDegree = useSharedValue(90);

  // 当前检测到的物理方向
  const [currentOrientation, setCurrentOrientation] =
    useState<PhysicalOrientation>(PHYSICAL_ORIENTATION.OTHER);

  // 防抖相关
  const debounceTimerRef = useRef<number | null>(null);
  const lastOrientationRef = useRef<PhysicalOrientation>(
    PHYSICAL_ORIENTATION.OTHER,
  );

  // 陀螺仪数据累积
  const rotationDataRef = useRef({
    z: 0, // Z轴旋转累积值
    lastTimestamp: 0,
  });

  // 处理方向变化
  const handleOrientationChange = useCallback(
    (newOrientation: PhysicalOrientation) => {
      // 防抖：避免频繁切换
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
        if (lastOrientationRef.current !== newOrientation) {
          lastOrientationRef.current = newOrientation;
          setCurrentOrientation(newOrientation);

          let targetDegree = 90; // 默认角度

          switch (newOrientation) {
            case PHYSICAL_ORIENTATION.LANDSCAPE_LEFT:
              targetDegree = 90;
              break;
            case PHYSICAL_ORIENTATION.LANDSCAPE_RIGHT:
              targetDegree = -90;
              break;
            default:
              // 其他方向保持默认90度
              targetDegree = 90;
              break;
          }

          // 使用动画过渡到目标角度
          rotationDegree.value = withTiming(targetDegree, {
            duration: 300,
          });

          console.log(
            `设备方向变化: ${newOrientation}, 旋转角度: ${targetDegree}度`,
          );
        }
      }, 150); // 150ms防抖延迟
    },
    [rotationDegree],
  );

  // 监听陀螺仪数据
  useEffect(() => {
    if (!isFullscreen) {
      // 非全屏状态下重置为默认角度和方向
      rotationDegree.value = withTiming(90, { duration: 300 });
      setCurrentOrientation(PHYSICAL_ORIENTATION.OTHER);
      lastOrientationRef.current = PHYSICAL_ORIENTATION.OTHER;

      // 重置陀螺仪数据
      rotationDataRef.current = { z: 0, lastTimestamp: 0 };

      // 清理防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      return;
    }

    let subscription: { remove: () => void } | null = null;

    const setupGyroscopeListener = async () => {
      try {
        // 设置更新频率：每100ms更新一次，获得更精确的旋转数据
        Gyroscope.setUpdateInterval(100);

        // 监听陀螺仪数据
        subscription = Gyroscope.addListener(({ x, y, z }) => {
          const currentTime = Date.now();

          // 初始化时间戳
          if (rotationDataRef.current.lastTimestamp === 0) {
            rotationDataRef.current.lastTimestamp = currentTime;
            return;
          }

          // 计算时间差（秒）
          const deltaTime =
            (currentTime - rotationDataRef.current.lastTimestamp) / 1000;
          rotationDataRef.current.lastTimestamp = currentTime;

          // 累积Z轴旋转（绕垂直轴旋转，即设备平面内的旋转）
          // z > 0: 逆时针旋转, z < 0: 顺时针旋转
          rotationDataRef.current.z += z * deltaTime;

          // 将累积旋转转换为角度（弧度转角度）
          const rotationAngle = (rotationDataRef.current.z * 180) / Math.PI;

          let detectedOrientation: PhysicalOrientation =
            PHYSICAL_ORIENTATION.OTHER;

          // 根据累积旋转角度判断设备方向
          // 标准化角度到 -180 到 180 度范围
          const normalizedAngle = ((rotationAngle % 360) + 360) % 360;
          const finalAngle =
            normalizedAngle > 180 ? normalizedAngle - 360 : normalizedAngle;

          // 判断设备方向
          if (Math.abs(finalAngle) > 45 && Math.abs(finalAngle) < 135) {
            if (finalAngle > 0) {
              // 逆时针旋转 45-135 度 -> LANDSCAPE_LEFT
              detectedOrientation = PHYSICAL_ORIENTATION.LANDSCAPE_LEFT;
            } else {
              // 顺时针旋转 45-135 度 -> LANDSCAPE_RIGHT
              detectedOrientation = PHYSICAL_ORIENTATION.LANDSCAPE_RIGHT;
            }
          }

          // 添加调试日志
          console.log(
            `陀螺仪数据: z=${z.toFixed(3)}, 累积角度=${rotationAngle.toFixed(
              1,
            )}°, 标准化角度=${finalAngle.toFixed(
              1,
            )}°, orientation=${detectedOrientation}`,
          );

          handleOrientationChange(detectedOrientation);
        });

        console.log('陀螺仪监听已启动');
      } catch (error) {
        console.warn('启动陀螺仪监听失败:', error);
      }
    };

    setupGyroscopeListener();

    return () => {
      if (subscription) {
        subscription.remove();
        console.log('陀螺仪监听已停止');
      }

      // 清理防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
    };
  }, [isFullscreen, handleOrientationChange, rotationDegree]);

  return {
    rotationDegree,
    currentOrientation,
  };
};
