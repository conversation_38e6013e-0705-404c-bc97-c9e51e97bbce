import { Accelerometer, Gyroscope } from 'expo-sensors';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useSharedValue, withTiming } from 'react-native-reanimated';

// 定义设备物理方向常量
const PHYSICAL_ORIENTATION = {
  LANDSCAPE_LEFT: 'LANDSCAPE_LEFT',
  LANDSCAPE_RIGHT: 'LANDSCAPE_RIGHT',
  OTHER: 'OTHER', // 包括竖屏和其他方向
} as const;

type PhysicalOrientation =
  (typeof PHYSICAL_ORIENTATION)[keyof typeof PHYSICAL_ORIENTATION];

/**
 * 全屏视频播放器方向管理hook
 * 使用感应器检测设备当前静态方向，根据方向自动调整播放器旋转角度
 */
export const useFullscreenOrientation = (isFullscreen: boolean) => {
  // 旋转角度，默认90度（保持现有逻辑）
  const rotationDegree = useSharedValue(90);

  // 当前检测到的物理方向
  const [currentOrientation, setCurrentOrientation] =
    useState<PhysicalOrientation>(PHYSICAL_ORIENTATION.OTHER);

  // 防抖相关
  const debounceTimerRef = useRef<number | null>(null);
  const lastOrientationRef = useRef<PhysicalOrientation>(
    PHYSICAL_ORIENTATION.OTHER,
  );

  // 处理方向变化
  const handleOrientationChange = useCallback(
    (newOrientation: PhysicalOrientation) => {
      // 防抖：避免频繁切换
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
        if (lastOrientationRef.current !== newOrientation) {
          lastOrientationRef.current = newOrientation;
          setCurrentOrientation(newOrientation);

          let targetDegree = 90; // 默认角度

          switch (newOrientation) {
            case PHYSICAL_ORIENTATION.LANDSCAPE_LEFT:
              targetDegree = 90;
              break;
            case PHYSICAL_ORIENTATION.LANDSCAPE_RIGHT:
              targetDegree = -90;
              break;
            default:
              // 其他方向保持默认90度
              targetDegree = 90;
              break;
          }

          // 使用动画过渡到目标角度
          rotationDegree.value = withTiming(targetDegree, {
            duration: 300,
          });

          console.log(
            `设备方向变化: ${newOrientation}, 旋转角度: ${targetDegree}度`,
          );
        }
      }, 150); // 150ms防抖延迟
    },
    [rotationDegree],
  );

  // 监听加速度计数据（静止时即为重力向量）
  useEffect(() => {
    if (!isFullscreen) {
      // 非全屏状态下重置为默认角度和方向
      rotationDegree.value = withTiming(90, { duration: 300 });
      setCurrentOrientation(PHYSICAL_ORIENTATION.OTHER);
      lastOrientationRef.current = PHYSICAL_ORIENTATION.OTHER;

      // 清理防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      return;
    }

    let subscription: { remove: () => void } | null = null;

    const setupAccelerometerListener = async () => {
      try {
        // 设置更新频率：每500ms更新一次，减少频繁计算，更适合检测静态方向
        Accelerometer.setUpdateInterval(500);

        // 监听加速度计数据
        // 当设备静止时，加速度计读数就是重力向量，指向地心
        subscription = Accelerometer.addListener(({ x, y, z }) => {
          // 计算总加速度，用于判断设备是否相对静止
          const totalAcceleration = Math.sqrt(x * x + y * y + z * z);

          // 只有当设备相对静止时（总加速度接近重力加速度9.8）才进行方向判断
          // 这样可以避免设备运动时的误判
          if (Math.abs(totalAcceleration - 9.8) > 3) {
            return; // 设备在运动，跳过方向判断
          }

          let detectedOrientation: PhysicalOrientation =
            PHYSICAL_ORIENTATION.OTHER;

          // 使用重力向量判断设备当前的静态方向
          // 当设备处于横屏状态时，重力主要作用在x轴上
          // 降低阈值到5，使其更容易触发
          if (Math.abs(x) > Math.abs(y) && Math.abs(x) > 5) {
            if (x > 0) {
              // 重力向右，设备左侧朝下 -> LANDSCAPE_LEFT
              detectedOrientation = PHYSICAL_ORIENTATION.LANDSCAPE_LEFT;
            } else {
              // 重力向左，设备右侧朝下 -> LANDSCAPE_RIGHT
              detectedOrientation = PHYSICAL_ORIENTATION.LANDSCAPE_RIGHT;
            }
          }

          // 添加调试日志
          console.log(
            `加速度计数据: x=${x.toFixed(2)}, y=${y.toFixed(2)}, z=${z.toFixed(
              2,
            )}, total=${totalAcceleration.toFixed(
              2,
            )}, orientation=${detectedOrientation}`,
          );
          // 如果重力主要在y轴上，说明是竖屏状态，保持OTHER

          handleOrientationChange(detectedOrientation);
        });

        console.log('加速度计监听已启动（静态方向检测）');
      } catch (error) {
        console.warn('启动加速度计监听失败:', error);
      }
    };

    setupAccelerometerListener();

    return () => {
      if (subscription) {
        subscription.remove();
        console.log('加速度计监听已停止');
      }

      // 清理防抖定时器
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
    };
  }, [isFullscreen, handleOrientationChange, rotationDegree]);

  return {
    rotationDegree,
    currentOrientation, // 可选：暴露当前方向用于调试
  };
};
