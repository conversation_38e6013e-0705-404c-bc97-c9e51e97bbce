{"expo": {"newArchEnabled": true, "name": "bookln", "slug": "bookln", "version": "8.8.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "bookln", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.yunti.zzm", "associatedDomains": ["applinks:bookln.cn", "applinks:www.bookln.cn", "applinks:mp.bookln.cn", "applinks:mp-daily.bookln.cn", "applinks:mp.bookln.cn"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.yunti.zzm", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "bookln.cn", "pathPrefix": "/web/app"}, {"scheme": "https", "host": "www.bookln.cn", "pathPrefix": "/web/app"}, {"scheme": "https", "host": "mp.bookln.cn", "pathPrefix": "/web/app"}, {"scheme": "https", "host": "mp-daily.bookln.cn", "pathPrefix": "/web/app"}, {"scheme": "https", "host": "mp-prepub.bookln.cn", "pathPrefix": "/web/app"}, {"scheme": "https", "host": "mp.zhizhuma.com", "pathPrefix": "/web/app"}, {"scheme": "https", "host": "mp-daily.zhizhuma.com", "pathPrefix": "/web/app"}, {"scheme": "https", "host": "mp-prepub.zhizhuma.com", "pathPrefix": "/web/app"}, {"scheme": "https", "host": "bookln.cn", "pathPrefix": "/webapp"}, {"scheme": "https", "host": "www.bookln.cn", "pathPrefix": "/webapp"}, {"scheme": "https", "host": "mp.bookln.cn", "pathPrefix": "/webapp"}, {"scheme": "https", "host": "mp-daily.bookln.cn", "pathPrefix": "/webapp"}, {"scheme": "https", "host": "mp-prepub.bookln.cn", "pathPrefix": "/webapp"}, {"scheme": "https", "host": "mp.zhizhuma.com", "pathPrefix": "/webapp"}, {"scheme": "https", "host": "mp-daily.zhizhuma.com", "pathPrefix": "/webapp"}, {"scheme": "https", "host": "mp-prepub.zhizhuma.com", "pathPrefix": "/webapp"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png", "name": "书链"}, "plugins": [["expo-router", {"origin": "https://bookln.cn/web/app", "asyncRoutes": {"web": true, "ios": false, "android": false, "default": "development"}}], "@config-plugins/react-native-blob-util", ["expo-build-properties", {"ios": {"newArchEnabled": true}, "android": {"newArchEnabled": true}}], ["@sentry/react-native/expo", {"url": "https://sentry.zhizhuma.com/", "project": "bookln-rn", "organization": "yunti"}], "expo-font", "expo-localization", "expo-web-browser", ["expo-image-picker", {"photosPermission": "允许访问相册"}], ["expo-video", {"supportsBackgroundPlayback": false, "supportsPictureInPicture": false}], "expo-audio", ["expo-sensors", {"motionPermission": "Allow $(PRODUCT_NAME) to access your device motion"}]], "experiments": {"typedRoutes": true}}}